import { useState, useEffect } from 'react'
import { User, Session, AuthError } from '@supabase/supabase-js'
import { supabase, type AuthUser, type AuthProvider } from '../config/supabase'

interface AuthState {
  user: AuthUser | null
  session: Session | null
  loading: boolean
  error: string | null
}

interface AuthActions {
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signUp: (email: string, password: string, name?: string) => Promise<{ error: AuthError | null }>
  signInWithProvider: (provider: AuthProvider) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<{ error: AuthError | null }>
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>
  clearError: () => void
}

export function useAuth(): AuthState & AuthActions {
  const [state, setState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true,
    error: null
  })

  // Converter User do Supabase para AuthUser
  const convertUser = (user: User | null): AuthUser | null => {
    if (!user) return null
    
    return {
      id: user.id,
      email: user.email || '',
      name: user.user_metadata?.name || user.user_metadata?.full_name,
      avatar_url: user.user_metadata?.avatar_url
    }
  }

  // Inicializar autenticação
  useEffect(() => {
    // Verificar sessão atual
    supabase.auth.getSession().then(({ data: { session }, error }) => {
      if (error) {
        console.error('Erro ao obter sessão:', error)
        setState(prev => ({ ...prev, error: error.message, loading: false }))
        return
      }

      setState(prev => ({
        ...prev,
        session,
        user: convertUser(session?.user || null),
        loading: false
      }))
    })

    // Escutar mudanças de autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email)
        
        setState(prev => ({
          ...prev,
          session,
          user: convertUser(session?.user || null),
          loading: false,
          error: null
        }))
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  // Fazer login com email e senha
  const signIn = async (email: string, password: string) => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) {
      setState(prev => ({ ...prev, error: error.message, loading: false }))
    }

    return { error }
  }

  // Registrar novo usuário
  const signUp = async (email: string, password: string, name?: string) => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: name || email.split('@')[0]
        }
      }
    })

    if (error) {
      setState(prev => ({ ...prev, error: error.message, loading: false }))
    }

    return { error }
  }

  // Login com provedor (Google, etc.)
  const signInWithProvider = async (provider: AuthProvider) => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    if (provider === 'email') {
      setState(prev => ({ ...prev, error: 'Use signIn para email/senha', loading: false }))
      return { error: new Error('Use signIn para email/senha') as AuthError }
    }

    const { error } = await supabase.auth.signInWithOAuth({
      provider: provider as any,
      options: {
        redirectTo: chrome?.runtime?.getURL?.('popup.html') || window.location.origin
      }
    })

    if (error) {
      setState(prev => ({ ...prev, error: error.message, loading: false }))
    }

    return { error }
  }

  // Fazer logout
  const signOut = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    const { error } = await supabase.auth.signOut()

    if (error) {
      setState(prev => ({ ...prev, error: error.message, loading: false }))
    }

    return { error }
  }

  // Resetar senha
  const resetPassword = async (email: string) => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: chrome?.runtime?.getURL?.('popup.html') || window.location.origin
    })

    if (error) {
      setState(prev => ({ ...prev, error: error.message, loading: false }))
    } else {
      setState(prev => ({ ...prev, loading: false }))
    }

    return { error }
  }

  // Limpar erro
  const clearError = () => {
    setState(prev => ({ ...prev, error: null }))
  }

  return {
    ...state,
    signIn,
    signUp,
    signInWithProvider,
    signOut,
    resetPassword,
    clearError
  }
}
