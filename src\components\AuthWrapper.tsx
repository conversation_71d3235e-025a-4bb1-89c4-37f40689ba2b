import React from 'react'
import { LogOut, User } from 'lucide-react'
import { useAuth } from '../hooks/useAuth'
import Auth from './Auth'
import Cha<PERSON><PERSON><PERSON><PERSON> from './ChatbotSOGE-minimal'
import { Button } from './ui/button'
import { Avatar, AvatarFallback } from './ui/avatar'

const AuthWrapper: React.FC = () => {
  const { user, loading, signOut } = useAuth()

  // Mostrar loading enquanto verifica autenticação
  if (loading) {
    return (
      <div className="min-h-[500px] flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-sm text-muted-foreground">Verificando autenticação...</p>
        </div>
      </div>
    )
  }

  // Se não estiver autenticado, mostrar tela de login
  if (!user) {
    return <Auth />
  }

  // Se estiver autenticado, mostrar chat com header de usuário
  return (
    <div className="h-full flex flex-col">
      {/* Header com informações do usuário */}
      <div className="flex items-center justify-between p-3 border-b bg-white">
        <div className="flex items-center gap-2">
          <Avatar className="h-8 w-8">
            <AvatarFallback className="bg-blue-100 text-blue-600">
              <User className="h-4 w-4" />
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <span className="text-sm font-medium">
              {user.name || user.email.split('@')[0]}
            </span>
            <span className="text-xs text-muted-foreground">
              {user.email}
            </span>
          </div>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => signOut()}
          className="text-muted-foreground hover:text-foreground"
        >
          <LogOut className="h-4 w-4" />
        </Button>
      </div>

      {/* Chat */}
      <div className="flex-1">
        <ChatbotSOGE />
      </div>
    </div>
  )
}

export default AuthWrapper
