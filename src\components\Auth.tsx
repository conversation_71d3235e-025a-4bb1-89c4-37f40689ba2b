import React, { useState } from 'react'
import { Mail, Lock, User, Eye, EyeOff, LogIn, UserPlus, AlertCircle } from 'lucide-react'
import { useAuth } from '../hooks/useAuth'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Label } from './ui/label'

type AuthMode = 'signin' | 'signup' | 'reset'

const Auth: React.FC = () => {
  const [mode, setMode] = useState<AuthMode>('signin')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [name, setName] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { signIn, signUp, resetPassword, loading, error, clearError } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (isSubmitting) return

    setIsSubmitting(true)
    clearError()

    try {
      let result
      
      switch (mode) {
        case 'signin':
          result = await signIn(email, password)
          break
        case 'signup':
          result = await signUp(email, password, name)
          break
        case 'reset':
          result = await resetPassword(email)
          if (!result.error) {
            alert('Email de recuperação enviado! Verifique sua caixa de entrada.')
            setMode('signin')
          }
          break
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetForm = () => {
    setEmail('')
    setPassword('')
    setName('')
    clearError()
  }

  const switchMode = (newMode: AuthMode) => {
    setMode(newMode)
    resetForm()
  }

  const getTitle = () => {
    switch (mode) {
      case 'signin': return 'Entrar no Chat'
      case 'signup': return 'Criar Conta'
      case 'reset': return 'Recuperar Senha'
    }
  }

  const getButtonText = () => {
    if (isSubmitting || loading) return 'Carregando...'
    
    switch (mode) {
      case 'signin': return 'Entrar'
      case 'signup': return 'Criar Conta'
      case 'reset': return 'Enviar Email'
    }
  }

  return (
    <div className="min-h-[500px] flex items-center justify-center p-4 bg-gradient-to-br from-blue-50 to-indigo-100">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center flex items-center justify-center gap-2">
            <LogIn className="h-6 w-6" />
            {getTitle()}
          </CardTitle>
          <p className="text-sm text-muted-foreground text-center">
            {mode === 'signin' && 'Entre com suas credenciais para acessar o chat'}
            {mode === 'signup' && 'Crie uma conta para começar a usar o chat'}
            {mode === 'reset' && 'Digite seu email para recuperar a senha'}
          </p>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Nome (apenas no signup) */}
            {mode === 'signup' && (
              <div className="space-y-2">
                <Label htmlFor="name">Nome</Label>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="name"
                    type="text"
                    placeholder="Seu nome"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="pl-10"
                    required={mode === 'signup'}
                  />
                </div>
              </div>
            )}

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="pl-10"
                  required
                />
              </div>
            </div>

            {/* Senha (não mostrar no reset) */}
            {mode !== 'reset' && (
              <div className="space-y-2">
                <Label htmlFor="password">Senha</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Sua senha"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="pl-10 pr-10"
                    required
                    minLength={6}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-3 text-muted-foreground hover:text-foreground"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>
            )}

            {/* Erro */}
            {error && (
              <div className="flex items-center gap-2 p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                <AlertCircle className="h-4 w-4" />
                {error}
              </div>
            )}

            {/* Botão de submit */}
            <Button
              type="submit"
              className="w-full"
              disabled={isSubmitting || loading}
            >
              {mode === 'signin' && <LogIn className="mr-2 h-4 w-4" />}
              {mode === 'signup' && <UserPlus className="mr-2 h-4 w-4" />}
              {mode === 'reset' && <Mail className="mr-2 h-4 w-4" />}
              {getButtonText()}
            </Button>
          </form>

          {/* Links de navegação */}
          <div className="mt-6 space-y-2 text-center text-sm">
            {mode === 'signin' && (
              <>
                <button
                  type="button"
                  onClick={() => switchMode('signup')}
                  className="text-blue-600 hover:text-blue-800 hover:underline"
                >
                  Não tem conta? Criar uma agora
                </button>
                <br />
                <button
                  type="button"
                  onClick={() => switchMode('reset')}
                  className="text-gray-600 hover:text-gray-800 hover:underline"
                >
                  Esqueceu a senha?
                </button>
              </>
            )}
            
            {mode === 'signup' && (
              <button
                type="button"
                onClick={() => switchMode('signin')}
                className="text-blue-600 hover:text-blue-800 hover:underline"
              >
                Já tem conta? Fazer login
              </button>
            )}
            
            {mode === 'reset' && (
              <button
                type="button"
                onClick={() => switchMode('signin')}
                className="text-blue-600 hover:text-blue-800 hover:underline"
              >
                Voltar para login
              </button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default Auth
